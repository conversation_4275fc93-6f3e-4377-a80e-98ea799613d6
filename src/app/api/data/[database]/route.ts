import { NextResponse } from 'next/server';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';
import { db } from '@/lib/drizzle';
import { count, and, or, eq, ilike, isNull, inArray, SQL } from 'drizzle-orm';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    const database = rawDatabase.toLowerCase();

    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    const config = await getDatabaseConfig(database);
    if (!config) {
      return NextResponse.json(
        { success: false, error: '未找到数据库配置' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);

    // 使用全局翻页配置（性能优化）
    const requestedPage = parseInt(searchParams.get('page') || '1', 10);
    const requestedLimit = parseInt(searchParams.get('limit') || '0', 10);

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 'asc' : 'desc';
    const filtersParam = searchParams.get('filters');
    const allFieldsQuery = searchParams.get('allFields')?.trim();

    // 如果有 allFields 查询，尝试使用统一搜索 API
    if (allFieldsQuery) {
      try {
        console.log('Using unified search for allFields:', allFieldsQuery, 'database:', database);

        const unifiedResponse = await fetch(
          `${request.url.split('/api/')[0]}/api/unified-database-search/${database}?q=${encodeURIComponent(allFieldsQuery)}&page=${page}&limit=${limit}`
        );

        if (unifiedResponse.ok) {
          const unifiedData = await unifiedResponse.json();

          if (unifiedData.success && unifiedData.data) {
            console.log('Unified search successful, returning results');

            return NextResponse.json({
              success: true,
              data: unifiedData.data,
              config: config,
              pagination: buildPaginationResponse(
                unifiedData.pagination?.page || page,
                unifiedData.pagination?.limit || limit,
                unifiedData.pagination?.total_results || 0
              ),
              search_info: unifiedData.search_info,
            });
          } else {
            console.warn('Unified search returned no data, falling back to Prisma');
          }
        } else {
          console.warn('Unified search response not ok, falling back to Prisma');
        }
      } catch (unifiedError) {
        console.warn('Unified search failed, falling back to Prisma:', unifiedError);
      }
    }

    const model = await getDynamicTable(database);

    // 验证 sortBy 字段是否有效
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 构建排序条件
    let orderBy: Record<string, 'asc' | 'desc'> = {};

    if (sortBy && sortableFields.includes(sortBy)) {
      // 如果指定了有效的排序字段，使用指定的排序
      orderBy = { [sortBy]: sortOrder };
    } else if (config.defaultSort && config.defaultSort.length > 0) {
      // 使用配置中的默认排序
      if (config.defaultSort.length === 1) {
        const sort = config.defaultSort[0];
        orderBy = { [sort.field]: sort.order };
      } else {
        // 多字段排序
        orderBy = config.defaultSort.map(sort => ({
          [sort.field]: sort.order
        })) as any;
      }
    } else {
      // 回退到第一个可排序字段或id
      if (sortableFields.includes('id')) {
        orderBy = { id: sortOrder };
      } else if (sortableFields.length > 0) {
        orderBy = { [sortableFields[0]]: sortOrder };
      }
    }

    // 构建 Drizzle where 条件
    let whereConditions: SQL[] = [];

    // 处理 allFields 全局搜索
    if (allFieldsQuery && allFieldsQuery.trim()) {
      const keyword = allFieldsQuery.trim();
      const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');

      if (searchableFields.length > 0) {
        const globalSearchConditions = searchableFields
          .map(f => {
            const column = (model as any)[f.fieldName];
            return column ? ilike(column, `%${keyword}%`) : null;
          })
          .filter(Boolean) as SQL[];

        if (globalSearchConditions.length > 0) {
          whereConditions.push(or(...globalSearchConditions)!);
        }
      }
    }

    // 处理其他筛选条件
    if (filtersParam) {
      try {
        const parsedFilters = JSON.parse(filtersParam);

        Object.entries(parsedFilters).forEach(([fieldName, value]) => {
          const column = (model as any)[fieldName];
          if (!column) return;

          // 查找字段配置
          const fieldConfig = config.fields.find(f => f.fieldName === fieldName);

          if (fieldConfig) {
            // 处理多选字段
            if (Array.isArray(value) && value.length > 0) {
              // 检查是否包含N/A值
              const processedValues = value.map(v => v === 'N/A' ? null : v);
              if (processedValues.includes(null)) {
                // 如果包含N/A，需要特殊处理
                const nonNullValues = processedValues.filter(v => v !== null);
                if (nonNullValues.length > 0) {
                  // 既有具体值又有N/A，使用OR条件
                  whereConditions.push(
                    or(
                      inArray(column, nonNullValues),
                      isNull(column),
                      eq(column, '')
                    )!
                  );
                } else {
                  // 只选择了N/A
                  whereConditions.push(
                    or(
                      isNull(column),
                      eq(column, '')
                    )!
                  );
                }
              } else {
                whereConditions.push(inArray(column, value));
              }
            }
            // 处理单选字段
            else if (value && typeof value === 'string' && value.trim() !== '') {
              if (value === 'N/A') {
                // 单选字段选择了N/A
                whereConditions.push(
                  or(
                    isNull(column),
                    eq(column, '')
                  )!
                );
              } else if (fieldConfig.searchType === 'exact') {
                whereConditions.push(eq(column, value));
              } else if (fieldConfig.searchType === 'contains') {
                whereConditions.push(ilike(column, `%${value}%`));
              } else {
                // 默认使用contains搜索
                whereConditions.push(ilike(column, `%${value}%`));
              }
            }
          } else {
            // 如果没有找到字段配置，使用默认的contains搜索
            if (Array.isArray(value) && value.length > 0) {
              whereConditions.push(inArray(column, value));
            } else if (value && typeof value === 'string' && value.trim() !== '') {
              whereConditions.push(ilike(column, `%${value}%`));
            }
          }
        });
      } catch (_e) {
        console.error("解析筛选条件失败:", _e);
      }
    }

    // 构建最终的 where 条件
    const finalWhere = whereConditions.length > 0
      ? (whereConditions.length === 1 ? whereConditions[0] : and(...whereConditions))
      : undefined;

    console.log('[API] 查询条件:', {
      database,
      allFieldsQuery,
      filtersParam,
      whereConditionsCount: whereConditions.length,
      page,
      limit
    });

    // 使用 Drizzle 语法查询数据
    let dataQuery = db
      .select()
      .from(model)
      .limit(limit)
      .offset((page - 1) * limit);

    if (finalWhere) {
      dataQuery = dataQuery.where(finalWhere);
    }

    const data = await dataQuery;

    // 使用 Drizzle 语法计算总数
    let countQuery = db
      .select({ count: count() })
      .from(model);

    if (finalWhere) {
      countQuery = countQuery.where(finalWhere);
    }

    const totalResult = await countQuery;
    const total = totalResult[0]?.count || 0;

    console.log('[API] 查询结果:', {
      dataCount: data.length,
      total,
      hasFilters: whereConditions.length > 0
    });

    return NextResponse.json({
      success: true,
      data,
      config,
      pagination: buildPaginationResponse(page, limit, total)
    });
  } catch (__error) {
    console.error(`数据 API 错误:`, __error);
    return NextResponse.json(
      { success: false, error: '内部服务器错误' },
      { status: 500 }
    );
  }
} 